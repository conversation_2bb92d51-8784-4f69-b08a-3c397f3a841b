// Enhanced POS Settings Sync Edge Function
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SyncRequest {
  terminal_id: string
  category?: string
  version?: number
  timestamp: string
  action?: 'sync' | 'heartbeat' | 'register'
}

interface HeartbeatData {
  terminal_id: string
  status: 'online' | 'offline' | 'error'
  version: string
  uptime: number
  memory_usage: number
  cpu_usage: number
  settings_hash: string
  sync_status: 'synced' | 'pending' | 'failed'
  pending_updates: number
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { terminal_id, category, version, timestamp, action = 'sync' }: SyncRequest = await req.json()

    console.log(`POS Sync Request: ${action} for terminal ${terminal_id}`)

    switch (action) {
      case 'heartbeat':
        return await handleHeartbeat(supabase, req)
      
      case 'register':
        return await handleTerminalRegistration(supabase, req)
      
      case 'sync':
      default:
        return await handleSettingsSync(supabase, terminal_id, category, version)
    }

  } catch (error) {
    console.error('POS Sync Error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Unknown error occurred'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})

async function handleHeartbeat(supabase: any, req: Request) {
  const heartbeatData: HeartbeatData = await req.json()
  
  try {
    // Update terminal status
    const { error: terminalError } = await supabase
      .from('pos_terminals')
      .update({
        status: heartbeatData.status,
        last_heartbeat: new Date().toISOString(),
        uptime: heartbeatData.uptime,
        version: heartbeatData.version
      })
      .eq('terminal_id', heartbeatData.terminal_id)

    if (terminalError) {
      throw new Error(`Failed to update terminal: ${terminalError.message}`)
    }

    // Store heartbeat record
    const { error: heartbeatError } = await supabase
      .from('pos_heartbeats')
      .insert({
        terminal_id: heartbeatData.terminal_id,
        timestamp: new Date().toISOString(),
        status: heartbeatData.status,
        version: heartbeatData.version,
        uptime: heartbeatData.uptime,
        memory_usage: heartbeatData.memory_usage,
        cpu_usage: heartbeatData.cpu_usage,
        settings_hash: heartbeatData.settings_hash,
        sync_status: heartbeatData.sync_status,
        pending_updates: heartbeatData.pending_updates
      })

    if (heartbeatError) {
      console.error('Failed to store heartbeat:', heartbeatError)
    }

    // Check for pending settings updates
    const { data: pendingConfigs, error: configError } = await supabase
      .from('pos_configurations')
      .select('*')
      .eq('terminal_id', heartbeatData.terminal_id)
      .eq('sync_status', 'pending')

    if (configError) {
      throw new Error(`Failed to check pending configs: ${configError.message}`)
    }

    const hasPendingUpdates = pendingConfigs && pendingConfigs.length > 0
    const settingsToSync = hasPendingUpdates ? groupSettingsByCategory(pendingConfigs) : null

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Heartbeat processed',
        has_pending_updates: hasPendingUpdates,
        settings_to_sync: settingsToSync,
        sync_required: heartbeatData.sync_status === 'pending' || hasPendingUpdates
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Heartbeat processing error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
}

async function handleTerminalRegistration(supabase: any, req: Request) {
  const registrationData = await req.json()
  
  try {
    const { error } = await supabase
      .from('pos_terminals')
      .upsert({
        terminal_id: registrationData.terminal_id,
        name: registrationData.name || `Terminal ${registrationData.terminal_id}`,
        location: registrationData.location || 'Unknown',
        ip_address: registrationData.ip_address,
        mac_address: registrationData.mac_address,
        status: 'online',
        last_heartbeat: new Date().toISOString(),
        version: registrationData.version || '1.0.0',
        uptime: 0,
        settings_version: 1,
        is_active: true
      }, {
        onConflict: 'terminal_id'
      })

    if (error) {
      throw new Error(`Registration failed: ${error.message}`)
    }

    // Get initial settings for this terminal
    const { data: configs, error: configError } = await supabase
      .from('pos_configurations')
      .select('*')
      .eq('terminal_id', registrationData.terminal_id)
      .eq('is_active', true)

    if (configError) {
      console.error('Failed to fetch initial configs:', configError)
    }

    const initialSettings = configs ? groupSettingsByCategory(configs) : {}

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Terminal registered successfully',
        terminal_id: registrationData.terminal_id,
        initial_settings: initialSettings
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Registration error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
}

async function handleSettingsSync(supabase: any, terminal_id: string, category?: string, version?: number) {
  try {
    let query = supabase
      .from('pos_configurations')
      .select('*')
      .eq('terminal_id', terminal_id)
      .eq('is_active', true)

    if (category) {
      query = query.eq('setting_category', category)
    }

    if (version) {
      query = query.eq('settings_version', version)
    }

    const { data: configurations, error } = await query

    if (error) {
      throw new Error(`Failed to fetch configurations: ${error.message}`)
    }

    if (!configurations || configurations.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No configurations found',
          settings: {}
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Group settings by category
    const groupedSettings = groupSettingsByCategory(configurations)

    // Log sync event
    await supabase
      .from('pos_settings_sync_history')
      .insert({
        terminal_id,
        setting_category: category || 'all',
        operation: 'sync',
        new_value: groupedSettings,
        sync_result: 'success',
        settings_version: version || 0
      })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Settings synchronized',
        settings: groupedSettings,
        timestamp: new Date().toISOString(),
        version: version || 0
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Settings sync error:', error)
    
    // Log failed sync
    await supabase
      .from('pos_settings_sync_history')
      .insert({
        terminal_id,
        setting_category: category || 'all',
        operation: 'sync',
        sync_result: 'failed',
        error_message: error.message,
        settings_version: version || 0
      })

    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
}

function groupSettingsByCategory(configurations: any[]) {
  const grouped: Record<string, Record<string, any>> = {}
  
  configurations.forEach(config => {
    if (!grouped[config.setting_category]) {
      grouped[config.setting_category] = {}
    }
    
    // Parse JSON values
    let value = config.setting_value
    if (typeof value === 'string') {
      try {
        value = JSON.parse(value)
      } catch {
        // Keep as string if not valid JSON
      }
    }
    
    grouped[config.setting_category][config.setting_key] = value
  })
  
  return grouped
}

// Utility function to validate settings structure
function validateSettingsStructure(settings: any): boolean {
  const requiredCategories = [
    'terminal', 'printer', 'tax', 'discount', 'receipt', 
    'payment', 'inventory', 'staff', 'restaurant'
  ]
  
  return requiredCategories.some(category => settings[category])
}