'use client'

import { useState } from 'react'
import { Users, UserPlus, Edit, UserX, UserCheck, Shield } from 'lucide-react'
import { GlassCard } from '@/components/ui/glass-components'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { toast } from 'react-hot-toast'
import { useTheme } from '@/contexts/theme-context'

interface POSStaff {
  id: string
  staff_code: string
  first_name: string
  last_name: string
  email: string
  role?: {
    id: string
    name: string
    display_name: string
  }
  is_active: boolean
  can_login_pos: boolean
  last_login_at?: string
}

interface StaffPermissions {
  can_process_refunds: boolean
  can_apply_discounts: boolean
  can_void_transactions: boolean
  can_open_cash_drawer: boolean
  can_access_reports: boolean
  can_modify_prices: boolean
  requires_manager_pin: boolean
  shift_management: boolean
  time_clock_integration: boolean
  break_reminders: boolean
}

interface StaffManagementProps {
  posStaff: POSStaff[]
  staffPermissions: StaffPermissions
  onShowAddStaff: () => void
  onStaffPermissionsChange: (permissions: StaffPermissions) => void
  onToggleStaffStatus: (staffId: string, isActive: boolean) => void
  onToggleStaffPOSAccess: (staffId: string, canLogin: boolean) => void
}

export function StaffManagement({
  posStaff,
  staffPermissions,
  onShowAddStaff,
  onStaffPermissionsChange,
  onToggleStaffStatus,
  onToggleStaffPOSAccess
}: StaffManagementProps) {
  const { isDarkTheme } = useTheme()
  const formatLastLogin = (lastLogin?: string) => {
    if (!lastLogin) return 'Never'
    return new Date(lastLogin).toLocaleDateString()
  }

  return (
    <div className="space-y-6">
      {/* Staff List */}
      <GlassCard className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className={`text-xl font-semibold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>POS Staff Management</h3>
          <Button onClick={onShowAddStaff} className="bg-green-600 hover:bg-green-700">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Staff
          </Button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className={`border-b transition-colors duration-1000 ${
                isDarkTheme ? 'border-white/10' : 'border-black/10'
              }`}>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>Staff Code</th>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>Name</th>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>Email</th>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>Role</th>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>Status</th>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>POS Access</th>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>Last Login</th>
                <th className={`text-left py-3 px-4 transition-colors duration-1000 ${
                  isDarkTheme ? 'text-white/80' : 'text-black/80'
                }`}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {posStaff.map((staff) => (
                <tr key={staff.id} className={`border-b transition-all duration-1000 ${
                  isDarkTheme
                    ? 'border-white/5 hover:bg-white/5'
                    : 'border-black/5 hover:bg-black/5'
                }`}>
                  <td className={`py-3 px-4 font-mono transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>{staff.staff_code}</td>
                  <td className={`py-3 px-4 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>
                    {staff.first_name} {staff.last_name}
                  </td>
                  <td className={`py-3 px-4 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/80' : 'text-black/80'
                  }`}>{staff.email}</td>
                  <td className="py-3 px-4">
                    <span className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-sm">
                      {staff.role?.display_name || 'No Role'}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <Switch
                      checked={staff.is_active}
                      onCheckedChange={(checked: boolean) => onToggleStaffStatus(staff.id, checked)}
                    />
                  </td>
                  <td className="py-3 px-4">
                    <Switch
                      checked={staff.can_login_pos}
                      onCheckedChange={(checked: boolean) => onToggleStaffPOSAccess(staff.id, checked)}
                    />
                  </td>
                  <td className="py-3 px-4 text-white/60">
                    {formatLastLogin(staff.last_login_at)}
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex gap-2">
                      <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="ghost" className="text-red-400 hover:text-red-300">
                        <UserX className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {posStaff.length === 0 && (
            <div className="text-center py-8 text-white/60">
              No staff members found. Click "Add Staff" to get started.
            </div>
          )}
        </div>
      </GlassCard>

      {/* Staff Permissions */}
      <GlassCard className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Shield className="h-5 w-5 text-blue-400" />
          <h3 className="text-xl font-semibold text-white">Default Staff Permissions</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Transaction Permissions */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-white">Transaction Permissions</h4>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Process Refunds</span>
              <Switch
                checked={staffPermissions.can_process_refunds}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, can_process_refunds: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Apply Discounts</span>
              <Switch
                checked={staffPermissions.can_apply_discounts}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, can_apply_discounts: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Void Transactions</span>
              <Switch
                checked={staffPermissions.can_void_transactions}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, can_void_transactions: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Open Cash Drawer</span>
              <Switch
                checked={staffPermissions.can_open_cash_drawer}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, can_open_cash_drawer: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Access Reports</span>
              <Switch
                checked={staffPermissions.can_access_reports}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, can_access_reports: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Modify Prices</span>
              <Switch
                checked={staffPermissions.can_modify_prices}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, can_modify_prices: checked })
                }
              />
            </div>
          </div>

          {/* System Permissions */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-white">System Permissions</h4>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Require Manager PIN</span>
              <Switch
                checked={staffPermissions.requires_manager_pin}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, requires_manager_pin: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Shift Management</span>
              <Switch
                checked={staffPermissions.shift_management}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, shift_management: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Time Clock Integration</span>
              <Switch
                checked={staffPermissions.time_clock_integration}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, time_clock_integration: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white/80">Break Reminders</span>
              <Switch
                checked={staffPermissions.break_reminders}
                onCheckedChange={(checked: boolean) => 
                  onStaffPermissionsChange({ ...staffPermissions, break_reminders: checked })
                }
              />
            </div>
          </div>
        </div>
      </GlassCard>
    </div>
  )
}