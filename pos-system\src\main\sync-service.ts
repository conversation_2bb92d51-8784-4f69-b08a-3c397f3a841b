import { DatabaseManager, Order, SyncQueue } from './database';
import { <PERSON><PERSON>erWindow } from 'electron';
import { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseClient } from '../shared/supabase-config';

export interface SyncStatus {
  isOnline: boolean;
  lastSync: string | null;
  pendingItems: number;
  syncInProgress: boolean;
  error: string | null;
}

export class SyncService {
  private supabase: SupabaseClient;
  private dbManager: DatabaseManager;
  private mainWindow: BrowserWindow | null = null;
  private syncInterval: NodeJS.Timeout | null = null;
  private realtimeChannel: any = null;
  private isOnline: boolean = false;
  private syncInProgress: boolean = false;
  private lastSync: string | null = null;

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager;
    this.supabase = getSupabaseClient();
    this.setupNetworkMonitoring();
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  private setupNetworkMonitoring(): void {
    // Check network status periodically
    setInterval(() => {
      this.checkNetworkStatus();
    }, 5000); // Check every 5 seconds
  }

  private async checkNetworkStatus(): Promise<void> {
    try {
      // Test Supabase connectivity by making a simple query
      const { data, error } = await this.supabase
        .from('orders')
        .select('count')
        .limit(1);
      
      const wasOnline = this.isOnline;
      this.isOnline = !error;
      
      // Only notify if status changed
      if (wasOnline !== this.isOnline) {
        this.notifyRenderer('network-status', { isOnline: this.isOnline });
        
        if (this.isOnline) {
        } else {
        }
      }
    } catch (error) {
      const wasOnline = this.isOnline;
      this.isOnline = false;
      
      if (wasOnline !== this.isOnline) {
        this.notifyRenderer('network-status', { isOnline: this.isOnline });
      }
    }
  }

  async startSync(): Promise<void> {
    if (this.syncInProgress || !this.isOnline) {
      return;
    }

    this.syncInProgress = true;
    this.notifyRenderer('sync-status', await this.getSyncStatus());

    try {
      
      // Test Supabase connection first
      const connectionTest = await this.testConnection();
      if (!connectionTest.success) {
        throw new Error(`Supabase connection failed: ${connectionTest.error}`);
      }

      // Sync local changes to remote
      await this.syncLocalToRemote();
      
      // Sync remote changes to local
      await this.syncRemoteToLocal();
      
      // Clean up old sync queue items
      await this.dbManager.clearOldSyncQueue();
      
      // Setup real-time subscriptions
      this.setupRealtimeSubscriptions();
      
      this.lastSync = new Date().toISOString();
      this.notifyRenderer('sync-complete', { timestamp: this.lastSync });
    } catch (error) {
      console.error('Sync failed:', error);
      this.notifyRenderer('sync-error', { error: (error as Error).message });
    } finally {
      this.syncInProgress = false;
      this.notifyRenderer('sync-status', await this.getSyncStatus());
    }
  }

  private async syncLocalToRemote(): Promise<void> {
    const syncQueue = await this.dbManager.getSyncQueue();
    
    for (const item of syncQueue) {
      try {
        await this.processSyncQueueItem(item);
        await this.dbManager.updateSyncQueueItem(item.id, true);
      } catch (error) {
        console.error(`Failed to sync item ${item.id}:`, error);
        await this.dbManager.updateSyncQueueItem(item.id, false, (error as Error).message);
      }
    }
  }

  private async processSyncQueueItem(item: SyncQueue): Promise<void> {
    const data = JSON.parse(item.data);
    
    switch (item.table_name) {
      case 'orders':
        await this.syncOrder(item.operation, item.record_id, data);
        break;
      default:
        throw new Error(`Unknown table: ${item.table_name}`);
    }
  }

  private async syncOrder(operation: string, recordId: string, data: any): Promise<void> {
    switch (operation) {
      case 'insert':
        // Insert order to Supabase
        const { data: insertedOrder, error: insertError } = await this.supabase
          .from('orders')
          .insert({
            customer_name: data.customer_name,
            customer_email: data.customer_email || null,
            customer_phone: data.customer_phone || null,
            order_type: data.order_type || 'takeaway',
            status: data.status,
            total_amount: data.total_amount,
            tax_amount: data.tax_amount || (data.total_amount * 0.1), // Default 10% tax
            discount_amount: data.discount_amount || null,
            payment_status: data.payment_status || 'pending',
            payment_method: data.payment_method || null,
            notes: data.notes || null,
            table_number: data.table_number || null,
            estimated_ready_time: data.estimated_ready_time || null,
            created_at: data.created_at || new Date().toISOString(),
            updated_at: data.updated_at || new Date().toISOString()
          })
          .select()
          .single();
        
        if (insertError) throw insertError;
        
        // Update local order with Supabase ID
        if (insertedOrder) {
          await this.dbManager.updateOrderSupabaseId(recordId, insertedOrder.id);
        }
        break;
        
      case 'update':
        // Get the order from local database to get supabase_id
        const localOrder = await this.dbManager.getOrderById(recordId);
        if (!localOrder || !localOrder.supabase_id) {
          throw new Error('Cannot update: Order not found or missing supabase_id');
        }
        
        // Update order in Supabase
        const { error: updateError } = await this.supabase
          .from('orders')
          .update({
            ...data,
            updated_at: new Date().toISOString()
          })
          .eq('id', localOrder.supabase_id);
        
        if (updateError) throw updateError;
        break;
        
      case 'delete':
        const orderToDelete = await this.dbManager.getOrderById(recordId);
        if (orderToDelete && orderToDelete.supabase_id) {
          // Delete order from Supabase
          const { error: deleteError } = await this.supabase
            .from('orders')
            .delete()
            .eq('id', orderToDelete.supabase_id);
          
          if (deleteError) throw deleteError;
        }
        break;
    }
  }

  private async syncRemoteToLocal(): Promise<void> {
    try {
      // Get the timestamp of the last sync
      const lastSyncTime = this.lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // Default to 24 hours ago
      
      // Fetch updated orders from Supabase
      const { data: remoteOrders, error } = await this.supabase
        .from('orders')
        .select('*')
        .gte('updated_at', lastSyncTime)
        .order('updated_at', { ascending: true });
      
      if (error) throw error;
      
      if (remoteOrders && remoteOrders.length > 0) {
        for (const remoteOrder of remoteOrders) {
          await this.mergeRemoteOrder(remoteOrder);
        }
        
        // Notify renderer about new orders
        this.notifyRenderer('orders-updated', { count: remoteOrders.length });
      }
    } catch (error) {
      console.error('Failed to sync remote to local:', error);
      throw error;
    }
  }

  private async mergeRemoteOrder(remoteOrder: any): Promise<void> {
    // Check if we have a local order with this supabase_id
    const localOrders = await this.dbManager.getOrders();
    const existingOrder = localOrders.find(order => order.supabase_id === remoteOrder.id);
    
    if (existingOrder) {
      // Update existing order if remote is newer
      const remoteUpdated = new Date(remoteOrder.updated_at);
      const localUpdated = new Date(existingOrder.updated_at);
      
      if (remoteUpdated > localUpdated) {
        // Update local order with remote data
        await this.dbManager.updateOrderStatus(existingOrder.id, remoteOrder.status);
      }
    } else {
      // Create new local order from remote data
      const newOrder = {
        customer_name: remoteOrder.customer_name,
        items: remoteOrder.items,
        total_amount: remoteOrder.total_amount,
        status: remoteOrder.status,
        supabase_id: remoteOrder.id
      };
      
      await this.dbManager.insertOrder(newOrder);
    }
  }

  async getSyncStatus(): Promise<SyncStatus> {
    const syncQueue = await this.dbManager.getSyncQueue();
    
    return {
      isOnline: this.isOnline,
      lastSync: this.lastSync,
      pendingItems: syncQueue.length,
      syncInProgress: this.syncInProgress,
      error: null
    };
  }

  startAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    // Auto-sync every 30 seconds when online
    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.startSync();
      }
    }, 30000);
  }

  stopAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  private notifyRenderer(channel: string, data: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  // Setup real-time subscriptions
  setupRealtimeSubscriptions(): void {
    if (!this.isOnline || !this.supabase) return;
    
    try {
      // Clean up existing subscriptions
      if (this.realtimeChannel) {
        this.supabase.removeChannel(this.realtimeChannel);
      }

      // Setup new real-time subscription for orders
      this.realtimeChannel = this.supabase
        .channel('pos_orders_sync')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'orders' },
          (payload) => {
            this.handleRealtimeOrderChange(payload);
          }
        )
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'customers' },
          (payload) => {
            this.handleRealtimeCustomerChange(payload);
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
          }
        });
    } catch (error) {
      console.error('Failed to setup real-time subscriptions:', error);
    }
  }

  private async handleRealtimeOrderChange(payload: any): Promise<void> {
    try {
      switch (payload.eventType) {
        case 'INSERT':
        case 'UPDATE':
          await this.mergeRemoteOrder(payload.new);
          this.notifyRenderer('order-realtime-update', payload.new);
          break;
          
        case 'DELETE':
          // Handle order deletion if needed
          this.notifyRenderer('order-realtime-delete', payload.old);
          break;
      }
    } catch (error) {
      console.error('Failed to handle real-time change:', error);
    }
  }

  private async handleRealtimeCustomerChange(payload: any): Promise<void> {
    try {
      // Customer changes don't need immediate sync to local DB
      // but we can notify the renderer for UI updates
      this.notifyRenderer('customer-realtime-update', payload);
    } catch (error) {
      console.error('Failed to handle real-time customer change:', error);
    }
  }

  private async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await this.supabase
        .from('orders')
        .select('count')
        .limit(1);
      
      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async forceSync(): Promise<void> {
    if (this.syncInProgress) {
      return;
    }
    
    await this.startSync();
  }

  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  // Cleanup method for shutdown
  cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.realtimeChannel && this.supabase) {
      this.supabase.removeChannel(this.realtimeChannel);
      this.realtimeChannel = null;
    }
  }
}