'use client'

import React, { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { X, Save, Eye, EyeOff, User, Mail, Phone, Shield, Building } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { useTheme } from '@/contexts/theme-context'
import { GlassModal, GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components'

interface Role {
  id: string
  name: string
  display_name: string
  color: string
}

interface Branch {
  id: string
  name: string
}

interface AddStaffModalProps {
  isOpen: boolean
  onClose: () => void
  onStaffAdded: () => void
}

interface StaffFormData {
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: string
  branch_id: string
  department: string
  employment_type: string
  hourly_rate: string
  pin: string
  can_login_pos: boolean
  can_login_admin: boolean
  emergency_contact_name: string
  emergency_contact_phone: string
  notes: string
}

const AddStaffModal = ({ isOpen, onClose, onStaffAdded }: AddStaffModalProps) => {
  const { isDarkTheme } = useTheme()
  const [roles, setRoles] = useState<Role[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(false)
  const [showPin, setShowPin] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  
  const [formData, setFormData] = useState<StaffFormData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    role_id: '',
    branch_id: '',
    department: '',
    employment_type: 'full-time',
    hourly_rate: '',
    pin: '',
    can_login_pos: true,
    can_login_admin: false,
    emergency_contact_name: '',
    emergency_contact_phone: '',
    notes: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (isOpen) {
      loadRoles()
      loadBranches()
      generateRandomPin()
    }
  }, [isOpen])

  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('id, name, display_name, color')
        .eq('is_active', true)
        .order('level')

      if (error) {
        // If roles table doesn't exist, set empty array
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('Roles table not found - staff management system not yet set up')
          setRoles([])
          return
        }
        throw error
      }
      setRoles(data || [])
    } catch (error) {
      console.error('Error loading roles:', error)
      setRoles([]) // Set empty array as fallback
    }
  }

  const loadBranches = async () => {
    try {
      const { data, error } = await supabase
        .from('branches')
        .select('id, name')
        .eq('is_active', true)
        .order('name')

      if (error) {
        // If branches table doesn't exist, set empty array
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('Branches table not found - branch management system not yet set up')
          setBranches([])
          return
        }
        throw error
      }
      setBranches(data || [])
    } catch (error) {
      console.error('Error loading branches:', error)
      setBranches([]) // Set empty array as fallback
    }
  }

  const generateRandomPin = () => {
    const pin = Math.floor(1000 + Math.random() * 9000).toString()
    setFormData(prev => ({ ...prev, pin }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Step 1 validation
    if (!formData.first_name.trim()) newErrors.first_name = 'First name is required'
    if (!formData.last_name.trim()) newErrors.last_name = 'Last name is required'
    if (!formData.email.trim()) newErrors.email = 'Email is required'
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) newErrors.email = 'Invalid email format'
    
    // Step 2 validation
    if (!formData.role_id) newErrors.role_id = 'Role is required'
    if (!formData.department.trim()) newErrors.department = 'Department is required'
    
    // Step 3 validation
    if (formData.can_login_pos && !formData.pin.trim()) newErrors.pin = 'PIN is required for POS access'
    else if (formData.pin && !/^\d{4,6}$/.test(formData.pin)) newErrors.pin = 'PIN must be 4-6 digits'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Please fix the errors before continuing')
      return
    }

    setLoading(true)
    
    try {
      // First create the auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: formData.email,
        password: Math.random().toString(36).slice(-8) + 'A1!', // Temporary password
        email_confirm: true
      })

      if (authError) throw authError

      // Create the staff record
      const { data: staffData, error: staffError } = await supabase
        .from('staff')
        .insert({
          user_id: authData.user.id,
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          phone: formData.phone || null,
          role_id: formData.role_id,
          branch_id: formData.branch_id || null,
          department: formData.department,
          employment_type: formData.employment_type,
          hourly_rate: formData.hourly_rate ? parseFloat(formData.hourly_rate) : null,
          can_login_pos: formData.can_login_pos,
          can_login_admin: formData.can_login_admin,
          emergency_contact_name: formData.emergency_contact_name || null,
          emergency_contact_phone: formData.emergency_contact_phone || null,
          notes: formData.notes || null,
          is_active: true
        })
        .select()
        .single()

      if (staffError) throw staffError

      // Set PIN if provided
      if (formData.pin && formData.can_login_pos) {
        const { error: pinError } = await supabase.rpc('set_staff_pin', {
          staff_uuid: staffData.id,
          new_pin: formData.pin
        })

        if (pinError) {
          console.error('Error setting PIN:', pinError)
          toast.error('Staff created but PIN could not be set')
        }
      }

      toast.success('Staff member added successfully')
      onStaffAdded()
      onClose()
      
      // Reset form
      setFormData({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        role_id: '',
        branch_id: '',
        department: '',
        employment_type: 'full-time',
        hourly_rate: '',
        pin: '',
        can_login_pos: true,
        can_login_admin: false,
        emergency_contact_name: '',
        emergency_contact_phone: '',
        notes: ''
      })
      setCurrentStep(1)
      
    } catch (error) {
      console.error('Error creating staff:', error)
      toast.error('Failed to create staff member')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof StaffFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const nextStep = () => {
    if (currentStep === 1) {
      // Validate step 1
      const step1Errors: Record<string, string> = {}
      if (!formData.first_name.trim()) step1Errors.first_name = 'First name is required'
      if (!formData.last_name.trim()) step1Errors.last_name = 'Last name is required'
      if (!formData.email.trim()) step1Errors.email = 'Email is required'
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) step1Errors.email = 'Invalid email format'
      
      if (Object.keys(step1Errors).length > 0) {
        setErrors(step1Errors)
        return
      }
    }
    
    if (currentStep === 2) {
      // Validate step 2
      const step2Errors: Record<string, string> = {}
      if (!formData.role_id) step2Errors.role_id = 'Role is required'
      if (!formData.department.trim()) step2Errors.department = 'Department is required'
      
      if (Object.keys(step2Errors).length > 0) {
        setErrors(step2Errors)
        return
      }
    }
    
    setCurrentStep(prev => Math.min(prev + 1, 3))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  if (!isOpen) return null

  return (
    <GlassModal isOpen={isOpen} onClose={onClose} title="Add New Staff Member">
      <div className="w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Progress Steps */}
        <div className={`px-6 py-4 border-b transition-colors duration-1000 ${
          isDarkTheme ? 'border-white/10' : 'border-gray-200'
        }`}>
          <div className="flex items-center">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full transition-all duration-300 ${
                  currentStep >= step 
                    ? 'bg-blue-600 text-white shadow-lg' 
                    : isDarkTheme 
                      ? 'bg-white/10 text-white/60 border border-white/20' 
                      : 'bg-gray-200 text-gray-600'
                }`}>
                  {step}
                </div>
                {step < 3 && (
                  <div className={`h-1 w-12 mx-2 rounded-full transition-all duration-300 ${
                    currentStep > step ? 'bg-blue-600' : isDarkTheme ? 'bg-white/10' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className={`flex justify-between mt-2 text-sm transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-gray-600'
          }`}>
            <span>Personal Info</span>
            <span>Role & Department</span>
            <span>Access & Security</span>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-6">
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      <User className="w-4 h-4 inline mr-1" />
                      First Name *
                    </label>
                    <input
                      type="text"
                      value={formData.first_name}
                      onChange={(e) => handleInputChange('first_name', e.target.value)}
                      className={`glass-input ${
                        errors.first_name ? 'border-red-500' : ''
                      }`}
                      placeholder="John"
                    />
                    {errors.first_name && (
                      <p className="mt-1 text-sm text-red-400">{errors.first_name}</p>
                    )}
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Last Name *
                    </label>
                    <input
                      type="text"
                      value={formData.last_name}
                      onChange={(e) => handleInputChange('last_name', e.target.value)}
                      className={`glass-input ${
                        errors.last_name ? 'border-red-500' : ''
                      }`}
                      placeholder="Doe"
                    />
                    {errors.last_name && (
                      <p className="mt-1 text-sm text-red-400">{errors.last_name}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Mail className="w-4 h-4 inline mr-1" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`glass-input ${
                      errors.email ? 'border-red-500' : ''
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-400">{errors.email}</p>
                  )}
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Phone className="w-4 h-4 inline mr-1" />
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="glass-input"
                    placeholder="+****************"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Emergency Contact Name
                    </label>
                    <input
                      type="text"
                      value={formData.emergency_contact_name}
                      onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
                      className="glass-input"
                      placeholder="Jane Doe"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Emergency Contact Phone
                    </label>
                    <input
                      type="tel"
                      value={formData.emergency_contact_phone}
                      onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
                      className="glass-input"
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Role & Department */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Shield className="w-4 h-4 inline mr-1" />
                    Role *
                  </label>
                  <select
                    value={formData.role_id}
                    onChange={(e) => handleInputChange('role_id', e.target.value)}
                    className={`glass-select ${
                      errors.role_id ? 'border-red-500' : ''
                    }`}
                  >
                    <option value="">Select a role</option>
                    {roles.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.display_name}
                      </option>
                    ))}
                  </select>
                  {errors.role_id && (
                    <p className="mt-1 text-sm text-red-400">{errors.role_id}</p>
                  )}
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Building className="w-4 h-4 inline mr-1" />
                    Branch
                  </label>
                  <select
                    value={formData.branch_id}
                    onChange={(e) => handleInputChange('branch_id', e.target.value)}
                    className="glass-select"
                  >
                    <option value="">Select a branch</option>
                    {branches.map(branch => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Department *
                    </label>
                    <select
                      value={formData.department}
                      onChange={(e) => handleInputChange('department', e.target.value)}
                      className={`glass-select ${
                        errors.department ? 'border-red-500' : ''
                      }`}
                    >
                      <option value="">Select department</option>
                      <option value="kitchen">Kitchen</option>
                      <option value="front">Front of House</option>
                      <option value="management">Management</option>
                      <option value="delivery">Delivery</option>
                    </select>
                    {errors.department && (
                      <p className="mt-1 text-sm text-red-400">{errors.department}</p>
                    )}
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Employment Type
                    </label>
                    <select
                      value={formData.employment_type}
                      onChange={(e) => handleInputChange('employment_type', e.target.value)}
                      className="glass-select"
                    >
                      <option value="full-time">Full Time</option>
                      <option value="part-time">Part Time</option>
                      <option value="contract">Contract</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    Hourly Rate ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.hourly_rate}
                    onChange={(e) => handleInputChange('hourly_rate', e.target.value)}
                    className="glass-input"
                    placeholder="15.00"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={3}
                    className="glass-input resize-none"
                    placeholder="Additional notes about this staff member..."
                  />
                </div>
              </div>
            )}

            {/* Step 3: Access & Security */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="pos_access"
                      checked={formData.pos_access}
                      onChange={(e) => handleInputChange('pos_access', e.target.checked)}
                      className="glass-checkbox"
                    />
                    <label 
                      htmlFor="pos_access" 
                      className={`text-sm font-medium transition-colors duration-1000 ${
                        isDarkTheme ? 'text-white/90' : 'text-gray-700'
                      }`}
                    >
                      <CreditCard className="w-4 h-4 inline mr-1" />
                      Allow POS System Access
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="admin_access"
                      checked={formData.admin_access}
                      onChange={(e) => handleInputChange('admin_access', e.target.checked)}
                      className="glass-checkbox"
                    />
                    <label 
                      htmlFor="admin_access" 
                      className={`text-sm font-medium transition-colors duration-1000 ${
                        isDarkTheme ? 'text-white/90' : 'text-gray-700'
                      }`}
                    >
                      <Settings className="w-4 h-4 inline mr-1" />
                      Allow Admin Dashboard Access
                    </label>
                  </div>
                </div>

                {formData.pos_access && (
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      <Lock className="w-4 h-4 inline mr-1" />
                      POS PIN
                    </label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <input
                          type={showPin ? "text" : "password"}
                          value={formData.pos_pin}
                          onChange={(e) => handleInputChange('pos_pin', e.target.value)}
                          className={`glass-input pr-10 ${
                            errors.pos_pin ? 'border-red-500' : ''
                          }`}
                          placeholder="Enter 4-digit PIN"
                          maxLength={4}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPin(!showPin)}
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded transition-colors duration-200 ${
                            isDarkTheme 
                              ? 'text-white/60 hover:text-white/80 hover:bg-white/10' 
                              : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {showPin ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <GlassButton
                        type="button"
                        onClick={generatePin}
                        variant="secondary"
                        size="sm"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </GlassButton>
                    </div>
                    {errors.pos_pin && (
                      <p className="mt-1 text-sm text-red-400">{errors.pos_pin}</p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className={`flex items-center justify-between px-6 py-4 border-t transition-colors duration-1000 ${
            isDarkTheme ? 'border-white/10' : 'border-gray-200'
          }`}>
            <div>
              {currentStep > 1 && (
                <GlassButton
                  type="button"
                  onClick={prevStep}
                  variant="secondary"
                >
                  Previous
                </GlassButton>
              )}
            </div>
            
            <div className="flex space-x-3">
              <GlassButton
                type="button"
                onClick={onClose}
                variant="secondary"
              >
                Cancel
              </GlassButton>
              
              {currentStep < 3 ? (
                <GlassButton
                  type="button"
                  onClick={nextStep}
                  variant="primary"
                >
                  Next
                </GlassButton>
              ) : (
                <GlassButton
                  type="submit"
                  disabled={loading}
                  variant="primary"
                  className="flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <UserPlus className="w-4 h-4 mr-2" />
                      Create Staff Member
                    </>
                  )}
                </GlassButton>
              )}
            </div>
          </div>
        </form>
      </div>
    </GlassModal>
  )
}

export default AddStaffModal