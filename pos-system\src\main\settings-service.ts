import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import { DatabaseManager } from './database';
import { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseClient } from '../shared/supabase-config';

export class SettingsService {
  private dbManager: DatabaseManager;
  private supabase: SupabaseClient;
  private terminalId: string;
  private mainWindow: BrowserWindow | null = null;
  private subscriptions: Map<string, any> = new Map();
  private lastSyncTimes: Map<string, Date> = new Map();
  private syncInProgress = false;

  constructor(dbManager: DatabaseManager, terminalId: string = 'terminal-001') {
    this.dbManager = dbManager;
    this.terminalId = terminalId;
    this.supabase = getSupabaseClient();
    
    this.setupIpcHandlers();
    this.initializeRealTimeSubscriptions();
  }

  setMainWindow(window: BrowserWindow) {
    this.mainWindow = window;
  }

  private sendToRenderer(channel: string, data: any) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  private setupIpcHandlers(): void {
    // Get local settings
    ipcMain.handle('settings:get-local', async () => {
      try {
        const settings = await this.dbManager.getLocalSettings();
        return settings;
      } catch (error) {
        console.error('Failed to get local settings:', error);
        return {};
      }
    });

    // Update local settings
    ipcMain.handle('settings:update-local', async (_, { settingType, settings }) => {
      try {
        await this.dbManager.updateLocalSettings(settingType, settings);
        
        // Also push to Supabase if online
        await this.pushSettingsToSupabase(settingType, settings);
        
        return { success: true };
      } catch (error) {
        console.error('Failed to update local settings:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
      }
    });

    // Force sync settings
    ipcMain.handle('settings:force-sync', async () => {
      try {
        await this.syncAllSettings();
        return { success: true };
      } catch (error) {
        console.error('Failed to force sync settings:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
      }
    });

    // Get terminal ID
    ipcMain.handle('settings:get-terminal-id', () => {
      return this.terminalId;
    });

    // Subscribe to real-time updates
    ipcMain.handle('settings:subscribe', async (_, tableName) => {
      return this.subscribeToTable(tableName);
    });

    // Unsubscribe from updates
    ipcMain.handle('settings:unsubscribe', async (_, subscriptionId) => {
      return this.unsubscribeFromTable(subscriptionId);
    });

    // Check sync status
    ipcMain.handle('settings:sync-status', async () => {
      return {
        syncInProgress: this.syncInProgress,
        lastSyncTimes: Object.fromEntries(this.lastSyncTimes),
        activeSubscriptions: this.subscriptions.size
      };
    });
  }

  private async initializeRealTimeSubscriptions(): Promise<void> {
    try {
      
      // Subscribe to POS configurations
      await this.subscribeToTable('pos_configurations');
      
      // Subscribe to restaurant settings
      await this.subscribeToTable('restaurant_settings');
      
      // Subscribe to payment settings
      await this.subscribeToTable('payment_settings');
      
      
    } catch (error) {
      console.error('❌ Failed to initialize real-time subscriptions:', error);
    }
  }

  private async subscribeToTable(tableName: string): Promise<string> {
    try {
      const subscriptionId = `${tableName}-${Date.now()}`;
      
      let channel = this.supabase.channel(subscriptionId);
      
      // Configure subscription based on table
      switch (tableName) {
        case 'pos_configurations':
          channel = channel.on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'pos_configurations',
              filter: `terminal_id=eq.${this.terminalId},terminal_id=is.null`
            },
            (payload) => this.handlePOSConfigurationChange(payload)
          );
          break;
          
        case 'restaurant_settings':
          channel = channel.on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'restaurant_settings'
            },
            (payload) => this.handleRestaurantSettingsChange(payload)
          );
          break;
          
        case 'payment_settings':
          channel = channel.on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'payment_settings'
            },
            (payload) => this.handlePaymentSettingsChange(payload)
          );
          break;
      }
      
      await channel.subscribe();
      this.subscriptions.set(subscriptionId, channel);
      
      return subscriptionId;
      
    } catch (error) {
      console.error(`❌ Failed to subscribe to ${tableName}:`, error);
      throw error;
    }
  }

  private async unsubscribeFromTable(subscriptionId: string): Promise<boolean> {
    try {
      const channel = this.subscriptions.get(subscriptionId);
      if (channel) {
        await this.supabase.removeChannel(channel);
        this.subscriptions.delete(subscriptionId);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ Failed to unsubscribe from ${subscriptionId}:`, error);
      return false;
    }
  }

  private async handlePOSConfigurationChange(payload: any): Promise<void> {
    try {
      
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      if (eventType === 'INSERT' || eventType === 'UPDATE') {
        const { config_type, config_key, config_value, terminal_id } = newRecord;
        
        // Only apply if it's for our terminal or global setting
        if (!terminal_id || terminal_id === this.terminalId) {
          // Update local database
          await this.dbManager.updatePOSLocalConfig(
            this.terminalId,
            config_type,
            config_key,
            config_value
          );
          
          // Notify renderer process
          this.sendToRenderer('settings-update', {
            type: config_type,
            settings: { [config_key]: config_value }
          });
          
        }
      }
      
    } catch (error) {
      console.error('❌ Failed to handle POS configuration change:', error);
    }
  }

  private async handleRestaurantSettingsChange(payload: any): Promise<void> {
    try {
      
      const { eventType, new: newRecord } = payload;
      
      if (eventType === 'INSERT' || eventType === 'UPDATE') {
        const { setting_key, setting_value } = newRecord;
        
        // Update local database
        await this.dbManager.updateRestaurantLocalConfig(
          setting_key,
          setting_value,
          typeof setting_value === 'number' ? 'number' : 
          typeof setting_value === 'boolean' ? 'boolean' : 'string'
        );
        
        // Notify renderer process
        this.sendToRenderer('settings-update', {
          type: 'restaurant',
          settings: { [setting_key]: setting_value }
        });
        
      }
      
    } catch (error) {
      console.error('❌ Failed to handle restaurant settings change:', error);
    }
  }

  private async handlePaymentSettingsChange(payload: any): Promise<void> {
    try {
      
      const { eventType, new: newRecord } = payload;
      
      if (eventType === 'INSERT' || eventType === 'UPDATE') {
        // Update local database
        await this.dbManager.updatePaymentLocalConfig(newRecord.provider, newRecord);
        
        // Notify renderer process
        this.sendToRenderer('settings-update', {
          type: 'payment',
          settings: newRecord
        });
        
      }
      
    } catch (error) {
      console.error('❌ Failed to handle payment settings change:', error);
    }
  }

  private async pushSettingsToSupabase(settingType: string, settings: any): Promise<void> {
    try {
      if (this.syncInProgress) {
        return;
      }

      
      switch (settingType) {
        case 'terminal':
        case 'display':
        case 'printer':
        case 'hardware':
          await this.pushPOSConfiguration(settingType, settings);
          break;
          
        case 'tax':
        case 'discount':
        case 'receipt':
        case 'inventory':
        case 'staff':
          await this.pushPOSConfiguration(settingType, settings);
          break;
          
        case 'restaurant':
          await this.pushRestaurantSettings(settings);
          break;
          
        case 'payment':
          await this.pushPaymentSettings(settings);
          break;
      }
      
      this.lastSyncTimes.set(settingType, new Date());
      
    } catch (error) {
      console.error(`❌ Failed to push ${settingType} settings:`, error);
    }
  }

  private async pushPOSConfiguration(configType: string, settings: any): Promise<void> {
    const promises = Object.entries(settings).map(async ([key, value]) => {
      const { error } = await this.supabase
        .from('pos_configurations')
        .upsert({
          terminal_id: this.terminalId,
          config_key: key,
          config_value: value,
          config_type: configType,
          description: `${configType} setting: ${key}`,
          is_active: true,
          sync_status: 'synced',
          last_sync_at: new Date().toISOString()
        }, {
          onConflict: 'terminal_id,config_key'
        });
      
      if (error) throw error;
    });
    
    await Promise.all(promises);
  }

  private async pushRestaurantSettings(settings: any): Promise<void> {
    const promises = Object.entries(settings).map(async ([key, value]) => {
      const { error } = await this.supabase
        .from('restaurant_settings')
        .upsert({
          setting_key: key,
          setting_value: value,
          setting_type: typeof value === 'number' ? 'number' : 
                       typeof value === 'boolean' ? 'boolean' : 'string',
          is_public: false,
          last_sync_at: new Date().toISOString()
        }, {
          onConflict: 'setting_key'
        });
      
      if (error) throw error;
    });
    
    await Promise.all(promises);
  }

  private async pushPaymentSettings(settings: any): Promise<void> {
    if (Array.isArray(settings)) {
      // Multiple payment settings
      for (const setting of settings) {
        await this.pushSinglePaymentSetting(setting);
      }
    } else {
      // Single payment setting
      await this.pushSinglePaymentSetting(settings);
    }
  }

  private async pushSinglePaymentSetting(setting: any): Promise<void> {
    const { error } = await this.supabase
      .from('payment_settings')
      .upsert({
        provider: setting.provider,
        provider_config: setting.provider_config,
        is_enabled: setting.is_enabled,
        processing_fee_percentage: setting.processing_fee_percentage,
        processing_fee_fixed: setting.processing_fee_fixed,
        last_sync_at: new Date().toISOString()
      }, {
        onConflict: 'provider'
      });
    
    if (error) throw error;
  }

  private async syncAllSettings(): Promise<void> {
    if (this.syncInProgress) {
      return;
    }

    try {
      this.syncInProgress = true;
      
      // Notify renderer of sync start
      this.sendToRenderer('sync-status', { 
        syncInProgress: true, 
        message: 'Starting settings sync...' 
      });
      
      // Sync restaurant settings
      await this.syncRestaurantSettings();
      
      // Sync POS configurations
      await this.syncPOSConfigurations();
      
      // Sync payment settings
      await this.syncPaymentSettings();
      
      
      // Notify renderer of sync completion
      this.sendToRenderer('sync-status', { 
        syncInProgress: false, 
        lastSync: new Date().toISOString(),
        message: 'Settings sync completed successfully' 
      });
      
    } catch (error) {
      console.error('❌ Settings sync failed:', error);
      
      // Notify renderer of sync error
      this.sendToRenderer('sync-status', { 
        syncInProgress: false, 
        error: error instanceof Error ? error.message : 'Sync failed',
        message: 'Settings sync failed' 
      });
      
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncRestaurantSettings(): Promise<void> {
    try {
      const { data: settings, error } = await this.supabase
        .from('restaurant_settings')
        .select('*')
        .eq('is_public', false);
      
      if (error) throw error;
      
      if (settings && settings.length > 0) {
        const restaurantSettings: Record<string, any> = {};
        
        for (const setting of settings) {
          restaurantSettings[setting.setting_key] = setting.setting_value;
          
          await this.dbManager.updateRestaurantLocalConfig(
            setting.setting_key,
            setting.setting_value,
            setting.setting_type || 'string'
          );
        }
        
        // Notify renderer
        this.sendToRenderer('settings-update', {
          type: 'restaurant',
          settings: restaurantSettings
        });
        
      }
      
      this.lastSyncTimes.set('restaurant', new Date());
      
    } catch (error) {
      console.error('❌ Failed to sync restaurant settings:', error);
    }
  }

  private async syncPOSConfigurations(): Promise<void> {
    try {
      const { data: configs, error } = await this.supabase
        .from('pos_configurations')
        .select('*')
        .or(`terminal_id.eq.${this.terminalId},terminal_id.is.null`)
        .eq('is_active', true);
      
      if (error) throw error;
      
      if (configs && configs.length > 0) {
        const groupedConfigs: Record<string, Record<string, any>> = {};
        
        for (const config of configs) {
          if (!groupedConfigs[config.config_type]) {
            groupedConfigs[config.config_type] = {};
          }
          groupedConfigs[config.config_type][config.config_key] = config.config_value;
          
          await this.dbManager.updatePOSLocalConfig(
            this.terminalId,
            config.config_type,
            config.config_key,
            config.config_value
          );
        }
        
        // Notify renderer for each config type
        for (const [configType, settings] of Object.entries(groupedConfigs)) {
          this.sendToRenderer('settings-update', {
            type: configType,
            settings
          });
        }
        
      }
      
      this.lastSyncTimes.set('pos_configurations', new Date());
      
    } catch (error) {
      console.error('❌ Failed to sync POS configurations:', error);
    }
  }

  private async syncPaymentSettings(): Promise<void> {
    try {
      const { data: settings, error } = await this.supabase
        .from('payment_settings')
        .select('*')
        .eq('is_enabled', true);
      
      if (error) throw error;
      
      if (settings && settings.length > 0) {
        for (const setting of settings) {
          await this.dbManager.updatePaymentLocalConfig(setting.provider, setting);
        }
        
        // Notify renderer
        this.sendToRenderer('settings-update', {
          type: 'payment',
          settings
        });
        
      }
      
      this.lastSyncTimes.set('payment', new Date());
      
    } catch (error) {
      console.error('❌ Failed to sync payment settings:', error);
    }
  }

  // Cleanup method
  async cleanup(): Promise<void> {
    try {
      
      // Unsubscribe from all channels
      for (const [subscriptionId, channel] of this.subscriptions) {
        await this.supabase.removeChannel(channel);
      }
      
      this.subscriptions.clear();
      
    } catch (error) {
      console.error('❌ Settings service cleanup failed:', error);
    }
  }
} 