'use client'

import { useState, useEffect } from 'react'
import { Settings } from 'lucide-react'
import { useTheme } from '@/contexts/theme-context'
import { SettingsManager, POSConfiguration, PaymentSetting, RestaurantSetting } from '@/lib/settings'
import { supabase } from '@/lib/supabase'
import { toast } from 'react-hot-toast'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { GlassCard } from '@/components/ui/glass-components'
import AddStaffModal from '@/components/staff/AddStaffModal'

// Import our new modular components
import { 
  TerminalManagement, 
  StaffManagement, 
  PaymentSettings, 
  RestaurantSettings 
} from '@/components'

interface POSTerminal {
  id: string
  name: string
  location: string
  isActive: boolean
  lastSync: string
  syncStatus: 'synced' | 'pending' | 'failed'
}

interface POSStaff {
  id: string
  staff_code: string
  first_name: string
  last_name: string
  email: string
  role?: {
    id: string
    name: string
    display_name: string
  }
  is_active: boolean
  can_login_pos: boolean
  last_login_at?: string
}

interface TerminalSettings {
  display_brightness: number
  screen_timeout: number
  audio_enabled: boolean
  receipt_auto_print: boolean
  cash_drawer_enabled: boolean
  barcode_scanner_enabled: boolean
  customer_display_enabled: boolean
  touch_sensitivity: string
}

interface PrinterSettings {
  receipt_printer_ip: string
  receipt_printer_port: number
  kitchen_printer_ip: string
  kitchen_printer_port: number
  paper_size: string
  print_logo: boolean
  print_order_number: boolean
  print_date_time: boolean
  copy_count: number
}

interface HardwareSettings {
  cash_drawer_port: string
  barcode_scanner_port: string
  card_reader_enabled: boolean
  scale_enabled: boolean
  scale_port: string
  loyalty_card_reader: boolean
  wifi_ssid: string
  ethernet_enabled: boolean
}

interface TaxSettings {
  default_tax_rate: number
  tax_inclusive: boolean
  tax_name: string
  tax_calculation_method: string
  multiple_tax_rates: boolean
  tax_exempt_enabled: boolean
  auto_calculate_tax: boolean
  tax_rounding: string
}

interface DiscountSettings {
  enable_discounts: boolean
  enable_coupons: boolean
  max_discount_percentage: number
  require_manager_approval: boolean
  discount_on_total: boolean
  discount_on_items: boolean
  loyalty_discounts: boolean
  employee_discount_rate: number
  senior_discount_rate: number
  military_discount_rate: number
}

interface ReceiptSettings {
  header_text: string
  footer_text: string
  show_logo: boolean
  show_qr_code: boolean
  show_social_media: boolean
  receipt_width: string
  font_size: string
  print_customer_copy: boolean
  print_merchant_copy: boolean
  email_receipts: boolean
  sms_receipts: boolean
}

interface InventorySettings {
  auto_sync: boolean
  sync_interval: number
  low_stock_alerts: boolean
  low_stock_threshold: number
  auto_disable_out_of_stock: boolean
  track_ingredient_usage: boolean
  batch_sync: boolean
  sync_on_order: boolean
  inventory_adjustments: boolean
  waste_tracking: boolean
}

interface StaffPermissions {
  can_process_refunds: boolean
  can_apply_discounts: boolean
  can_void_transactions: boolean
  can_open_cash_drawer: boolean
  can_access_reports: boolean
  can_modify_prices: boolean
  requires_manager_pin: boolean
  shift_management: boolean
  time_clock_integration: boolean
  break_reminders: boolean
}

export default function POSPage() {
  const { isDarkTheme } = useTheme()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('terminals')
  const [posConfigs, setPosConfigs] = useState<POSConfiguration[]>([])
  const [paymentSettings, setPaymentSettings] = useState<PaymentSetting[]>([])
  const [restaurantSettings, setRestaurantSettings] = useState<RestaurantSetting[]>([])
  
  // Terminal management state
  const [terminals, setTerminals] = useState<POSTerminal[]>([
    {
      id: 'terminal-001',
      name: 'Main Counter',
      location: 'Front Desk',
      isActive: true,
      lastSync: new Date().toISOString(),
      syncStatus: 'synced'
    },
    {
      id: 'terminal-002', 
      name: 'Kitchen Display',
      location: 'Kitchen',
      isActive: true,
      lastSync: new Date(Date.now() - 300000).toISOString(),
      syncStatus: 'pending'
    }
  ])

  const [selectedTerminal, setSelectedTerminal] = useState('terminal-001')
  const [terminalSettings, setTerminalSettings] = useState<TerminalSettings>({
    display_brightness: 80,
    screen_timeout: 300,
    audio_enabled: true,
    receipt_auto_print: true,
    cash_drawer_enabled: true,
    barcode_scanner_enabled: true,
    customer_display_enabled: false,
    touch_sensitivity: 'medium'
  })

  const [printerSettings, setPrinterSettings] = useState<PrinterSettings>({
    receipt_printer_ip: '*************',
    receipt_printer_port: 9100,
    kitchen_printer_ip: '*************',
    kitchen_printer_port: 9100,
    paper_size: '80mm',
    print_logo: true,
    print_order_number: true,
    print_date_time: true,
    copy_count: 1
  })

  const [hardwareSettings, setHardwareSettings] = useState<HardwareSettings>({
    cash_drawer_port: 'COM1',
    barcode_scanner_port: 'USB',
    card_reader_enabled: true,
    scale_enabled: false,
    scale_port: 'COM2',
    loyalty_card_reader: false,
    wifi_ssid: 'Restaurant-POS',
    ethernet_enabled: true
  })

  const [taxSettings, setTaxSettings] = useState<TaxSettings>({
    default_tax_rate: 8.25,
    tax_inclusive: false,
    tax_name: 'Sales Tax',
    tax_calculation_method: 'percentage',
    multiple_tax_rates: false,
    tax_exempt_enabled: true,
    auto_calculate_tax: true,
    tax_rounding: 'nearest_cent'
  })

  const [discountSettings, setDiscountSettings] = useState<DiscountSettings>({
    enable_discounts: true,
    enable_coupons: true,
    max_discount_percentage: 50,
    require_manager_approval: true,
    discount_on_total: true,
    discount_on_items: true,
    loyalty_discounts: true,
    employee_discount_rate: 10,
    senior_discount_rate: 5,
    military_discount_rate: 10
  })

  const [receiptSettings, setReceiptSettings] = useState<ReceiptSettings>({
    header_text: 'Thank you for your visit!',
    footer_text: 'Visit us again soon!',
    show_logo: true,
    show_qr_code: true,
    show_social_media: true,
    receipt_width: '80mm',
    font_size: 'medium',
    print_customer_copy: true,
    print_merchant_copy: false,
    email_receipts: true,
    sms_receipts: false
  })

  const [inventorySettings, setInventorySettings] = useState<InventorySettings>({
    auto_sync: true,
    sync_interval: 30,
    low_stock_alerts: true,
    low_stock_threshold: 10,
    auto_disable_out_of_stock: true,
    track_ingredient_usage: true,
    batch_sync: false,
    sync_on_order: true,
    inventory_adjustments: true,
    waste_tracking: true
  })

  const [staffPermissions, setStaffPermissions] = useState<StaffPermissions>({
    can_process_refunds: false,
    can_apply_discounts: false,
    can_void_transactions: false,
    can_open_cash_drawer: true,
    can_access_reports: false,
    can_modify_prices: false,
    requires_manager_pin: true,
    shift_management: true,
    time_clock_integration: true,
    break_reminders: true
  })

  // Staff management state
  const [posStaff, setPosStaff] = useState<POSStaff[]>([])
  const [showAddStaffModal, setShowAddStaffModal] = useState(false)

  useEffect(() => {
    loadPOSSettings()
    loadPOSStaff()
  }, [])

  const loadPOSSettings = async () => {
    try {
      setLoading(true)
      const configs = await SettingsManager.getPOSConfigurations()
      const payments = await SettingsManager.getPaymentSettings()
      const restaurant = await SettingsManager.getRestaurantSettings()
      
      setPosConfigs(configs)
      setPaymentSettings(payments)
      setRestaurantSettings(restaurant)
    } catch (error) {
      toast.error('Failed to load POS settings')
    } finally {
      setLoading(false)
    }
  }

  const loadPOSStaff = async () => {
    try {
      const { data: staff, error } = await supabase
        .from('staff')
        .select(`
          id,
          staff_code,
          first_name,
          last_name,
          email,
          is_active,
          can_login_pos,
          last_login_at,
          roles!inner (
            id,
            name,
            display_name
          )
        `)
        .eq('can_login_pos', true)
        .order('first_name')

      if (error) throw error
      
      // Transform the data to match our interface
      const transformedStaff = staff?.map((s: any) => ({
        ...s,
        role: Array.isArray(s.roles) ? s.roles[0] : s.roles
      })) || []
      
      setPosStaff(transformedStaff as POSStaff[])
    } catch (error) {
      toast.error('Failed to load POS staff')
    }
  }

  const handleSyncTerminal = (terminalId: string) => {
    setTerminals(prev => prev.map(terminal => 
      terminal.id === terminalId 
        ? { ...terminal, syncStatus: 'pending', lastSync: new Date().toISOString() }
        : terminal
    ))
    
    setTimeout(() => {
      setTerminals(prev => prev.map(terminal => 
        terminal.id === terminalId 
          ? { ...terminal, syncStatus: 'synced' }
          : terminal
      ))
      toast.success('Terminal synced successfully')
    }, 2000)
  }

  const handleToggleStaffStatus = async (staffId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('staff')
        .update({ is_active: isActive })
        .eq('id', staffId)

      if (error) throw error
      
      setPosStaff(prev => prev.map(staff => 
        staff.id === staffId ? { ...staff, is_active: isActive } : staff
      ))
      
      toast.success(`Staff ${isActive ? 'activated' : 'deactivated'} successfully`)
    } catch (error) {
      toast.error('Failed to update staff status')
    }
  }

  const handleToggleStaffPOSAccess = async (staffId: string, canLogin: boolean) => {
    try {
      const { error } = await supabase
        .from('staff')
        .update({ can_login_pos: canLogin })
        .eq('id', staffId)

      if (error) throw error
      
      setPosStaff(prev => prev.map(staff => 
        staff.id === staffId ? { ...staff, can_login_pos: canLogin } : staff
      ))
      
      toast.success(`POS access ${canLogin ? 'granted' : 'revoked'} successfully`)
    } catch (error) {
      toast.error('Failed to update POS access')
    }
  }

  return (
      <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
        {/* Page Header */}
        <div>
          <h1 className={`text-3xl font-bold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>POS Configuration</h1>
          <p className={`mt-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>
            Configure POS terminals, staff access, payment settings, and restaurant operations
          </p>
        </div>

      <GlassCard className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className={`transition-all duration-1000 ${
            isDarkTheme ? 'bg-white/5 border-white/10' : 'bg-black/5 border-black/10'
          }`}>
            <TabsTrigger value="terminals" className={`transition-all duration-1000 ${
              isDarkTheme ? 'data-[state=active]:bg-white/10 text-white/80 data-[state=active]:text-white' : 'data-[state=active]:bg-black/10 text-black/80 data-[state=active]:text-black'
            }`}>
              Terminals
            </TabsTrigger>
            <TabsTrigger value="staff" className={`transition-all duration-1000 ${
              isDarkTheme ? 'data-[state=active]:bg-white/10 text-white/80 data-[state=active]:text-white' : 'data-[state=active]:bg-black/10 text-black/80 data-[state=active]:text-black'
            }`}>
              Staff
            </TabsTrigger>
            <TabsTrigger value="payment" className={`transition-all duration-1000 ${
              isDarkTheme ? 'data-[state=active]:bg-white/10 text-white/80 data-[state=active]:text-white' : 'data-[state=active]:bg-black/10 text-black/80 data-[state=active]:text-black'
            }`}>
              Payment
            </TabsTrigger>
            <TabsTrigger value="restaurant" className={`transition-all duration-1000 ${
              isDarkTheme ? 'data-[state=active]:bg-white/10 text-white/80 data-[state=active]:text-white' : 'data-[state=active]:bg-black/10 text-black/80 data-[state=active]:text-black'
            }`}>
              Restaurant
            </TabsTrigger>
          </TabsList>

          <TabsContent value="terminals" className="space-y-6">
            <TerminalManagement
              terminals={terminals}
              selectedTerminal={selectedTerminal}
              terminalSettings={terminalSettings}
              hardwareSettings={hardwareSettings}
              onTerminalSelect={setSelectedTerminal}
              onTerminalSettingsChange={setTerminalSettings}
              onHardwareSettingsChange={setHardwareSettings}
              onSyncTerminal={handleSyncTerminal}
            />
          </TabsContent>

          <TabsContent value="staff" className="space-y-6">
            <StaffManagement
              posStaff={posStaff}
              staffPermissions={staffPermissions}
              onShowAddStaff={() => setShowAddStaffModal(true)}
              onStaffPermissionsChange={setStaffPermissions}
              onToggleStaffStatus={handleToggleStaffStatus}
              onToggleStaffPOSAccess={handleToggleStaffPOSAccess}
            />
          </TabsContent>

          <TabsContent value="payment" className="space-y-6">
            <PaymentSettings
              taxSettings={taxSettings}
              discountSettings={discountSettings}
              onTaxSettingsChange={setTaxSettings}
              onDiscountSettingsChange={setDiscountSettings}
            />
          </TabsContent>

          <TabsContent value="restaurant" className="space-y-6">
            <RestaurantSettings
              receiptSettings={receiptSettings}
              printerSettings={printerSettings}
              inventorySettings={inventorySettings}
              onReceiptSettingsChange={setReceiptSettings}
              onPrinterSettingsChange={setPrinterSettings}
              onInventorySettingsChange={setInventorySettings}
            />
          </TabsContent>
        </Tabs>
      </GlassCard>

      {/* Add Staff Modal */}
      {showAddStaffModal && (
        <AddStaffModal
          isOpen={showAddStaffModal}
          onClose={() => setShowAddStaffModal(false)}
          onStaffAdded={() => {
            loadPOSStaff()
            setShowAddStaffModal(false)
          }}
        />
      )}
      </div>
  )
}