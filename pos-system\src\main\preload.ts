import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define types for electron API
interface ElectronAPI {
  // Window controls
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;
  
  // Settings
  getSettings: () => Promise<any>;
  updateSettings: (settings: Record<string, any>) => Promise<void>;
  
  // Database
  executeQuery: (query: string, params?: any[]) => Promise<any>;
  
  // Auth
  login: (credentials: LoginCredentials) => Promise<any>;
  logout: () => Promise<void>;
  
  // System
  getSystemInfo: () => Promise<SystemInfo>;
  
  // Development helpers
  openDevTools: () => Promise<void>;
  reload: () => Promise<void>;
}

interface LoginCredentials {
  username?: string;
  password?: string;
  pin?: string;
  staffId?: string;
}

interface SystemInfo {
  platform: string;
  version: string;
  arch: string;
  appVersion: string;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Window controls
  minimize: () => ipcRenderer.invoke('window-minimize'),
  maximize: () => ipcRenderer.invoke('window-maximize'),
  close: () => ipcRenderer.invoke('window-close'),
  
  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  updateSettings: (settings: Record<string, any>) => ipcRenderer.invoke('update-settings', settings),
  
  // Database
  executeQuery: (query: string, params?: any[]) => ipcRenderer.invoke('execute-query', query, params),
  
  // Auth
  login: (credentials: LoginCredentials) => ipcRenderer.invoke('auth-login', credentials),
  logout: () => ipcRenderer.invoke('auth-logout'),
  
  // System
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  
  // Development helpers
  openDevTools: () => ipcRenderer.invoke('open-dev-tools'),
  reload: () => ipcRenderer.invoke('reload'),
} as ElectronAPI);