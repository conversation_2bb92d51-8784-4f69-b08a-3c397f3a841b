'use client'

import { useState, useEffect } from 'react'
import { enhancedPOSSettings, POSTerminal, SyncResult } from '@/lib/enhanced-pos-settings'
import { 
  POSSettingCategory, 
  DefaultPOSSettings,
  TerminalSettings,
  PrinterSettings,
  TaxSettings,
  PaymentSettings,
  ReceiptSettings
} from '@/lib/pos-settings-validation'
import { toast } from 'react-hot-toast'
import {
  Settings,
  Monitor,
  Printer,
  CreditCard,
  Receipt,
  Calculator,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  AlertTriangle,
  Save,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

interface EnhancedPOSManagerProps {
  onSyncStatusChange?: (status: string) => void
}

export default function EnhancedPOSManager({ onSyncStatusChange }: EnhancedPOSManagerProps) {
  const [terminals, setTerminals] = useState<POSTerminal[]>([])
  const [selectedTerminal, setSelectedTerminal] = useState<string>('')
  const [activeCategory, setActiveCategory] = useState<POSSettingCategory>('terminal')
  const [settings, setSettings] = useState<any>(DefaultPOSSettings)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [syncing, setSyncing] = useState(false)

  // Category configurations
  const categories = [
    { id: 'terminal' as POSSettingCategory, name: 'Terminal', icon: Monitor, description: 'Display and hardware settings' },
    { id: 'printer' as POSSettingCategory, name: 'Printer', icon: Printer, description: 'Receipt and kitchen printer setup' },
    { id: 'payment' as POSSettingCategory, name: 'Payment', icon: CreditCard, description: 'Payment processing options' },
    { id: 'tax' as POSSettingCategory, name: 'Tax', icon: Calculator, description: 'Tax calculation rules' },
    { id: 'receipt' as POSSettingCategory, name: 'Receipt', icon: Receipt, description: 'Receipt formatting and content' },
  ]

  useEffect(() => {
    loadTerminals()
  }, [])

  useEffect(() => {
    if (selectedTerminal) {
      loadTerminalSettings(selectedTerminal)
    }
  }, [selectedTerminal, activeCategory])

  const loadTerminals = async () => {
    try {
      const terminalList = await enhancedPOSSettings.getTerminals()
      setTerminals(terminalList)
      
      if (terminalList.length > 0 && !selectedTerminal) {
        setSelectedTerminal(terminalList[0].terminal_id)
      }
    } catch (error) {
      console.error('Failed to load terminals:', error)
      toast.error('Failed to load terminals')
    } finally {
      setLoading(false)
    }
  }

  const loadTerminalSettings = async (terminalId: string) => {
    try {
      const configurations = await enhancedPOSSettings.getPOSConfiguration(terminalId, activeCategory)
      
      // Convert to settings object
      const categorySettings: any = {}
      configurations.forEach(config => {
        categorySettings[config.setting_key] = config.setting_value
      })

      setSettings((prev: any) => ({
        ...prev,
        [activeCategory]: {
          ...DefaultPOSSettings[activeCategory],
          ...categorySettings
        }
      }))

    } catch (error) {
      console.error('Failed to load terminal settings:', error)
      toast.error('Failed to load settings')
    }
  }

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev: any) => ({
      ...prev,
      [activeCategory]: {
        ...prev[activeCategory],
        [key]: value
      }
    }))
  }

  const saveSettings = async () => {
    if (!selectedTerminal) {
      toast.error('No terminal selected')
      return
    }

    setSaving(true)
    try {
      const result = await enhancedPOSSettings.updatePOSConfiguration(
        selectedTerminal,
        activeCategory,
        settings[activeCategory]
      )

      if (result.success) {
        toast.success(`${activeCategory} settings saved successfully`)
        onSyncStatusChange?.('syncing')
      } else {
        toast.error(`Failed to save settings: ${result.errors.join(', ')}`)
      }
    } catch (error) {
      console.error('Failed to save settings:', error)
      toast.error('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const syncAllSettings = async () => {
    if (!selectedTerminal) {
      toast.error('No terminal selected')
      return
    }

    setSyncing(true)
    try {
      const results = await enhancedPOSSettings.syncAllSettings(selectedTerminal)
      
      const failedCategories = Object.entries(results)
        .filter(([_, result]) => !result.success)
        .map(([category, _]) => category)

      if (failedCategories.length === 0) {
        toast.success('All settings synchronized successfully')
        onSyncStatusChange?.('synced')
      } else {
        toast.error(`Failed to sync: ${failedCategories.join(', ')}`)
        onSyncStatusChange?.('failed')
      }

      // Refresh terminal list to update sync status
      await loadTerminals()

    } catch (error) {
      console.error('Failed to sync settings:', error)
      toast.error('Failed to sync settings')
      onSyncStatusChange?.('failed')
    } finally {
      setSyncing(false)
    }
  }

  const getTerminalStatusIcon = (status: POSTerminal['status']) => {
    switch (status) {
      case 'online':
        return <Wifi className="w-4 h-4 text-green-400" />
      case 'offline':
        return <WifiOff className="w-4 h-4 text-gray-400" />
      case 'syncing':
        return <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-400" />
      default:
        return <WifiOff className="w-4 h-4 text-gray-400" />
    }
  }

  const renderTerminalSettings = () => {
    const categorySettings = settings[activeCategory] || {}

    switch (activeCategory) {
      case 'terminal':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Display Brightness (%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={categorySettings.display_brightness || 80}
                  onChange={(e) => handleSettingChange('display_brightness', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
                <span className="text-sm text-gray-500">{categorySettings.display_brightness || 80}%</span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Screen Timeout (seconds)
                </label>
                <input
                  type="number"
                  min="30"
                  max="3600"
                  value={categorySettings.screen_timeout || 300}
                  onChange={(e) => handleSettingChange('screen_timeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Touch Sensitivity
                </label>
                <select
                  value={categorySettings.touch_sensitivity || 'medium'}
                  onChange={(e) => handleSettingChange('touch_sensitivity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>

              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.audio_enabled ?? true}
                    onChange={(e) => handleSettingChange('audio_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Audio Enabled</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.receipt_auto_print ?? true}
                    onChange={(e) => handleSettingChange('receipt_auto_print', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Auto Print Receipts</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.cash_drawer_enabled ?? true}
                    onChange={(e) => handleSettingChange('cash_drawer_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Cash Drawer Enabled</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.barcode_scanner_enabled ?? false}
                    onChange={(e) => handleSettingChange('barcode_scanner_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Barcode Scanner</span>
                </label>
              </div>
            </div>
          </div>
        )

      case 'printer':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Receipt Printer IP
                </label>
                <input
                  type="text"
                  placeholder="*************"
                  value={categorySettings.receipt_printer_ip || ''}
                  onChange={(e) => handleSettingChange('receipt_printer_ip', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Printer Port
                </label>
                <input
                  type="number"
                  min="1"
                  max="65535"
                  value={categorySettings.receipt_printer_port || 9100}
                  onChange={(e) => handleSettingChange('receipt_printer_port', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Paper Size
                </label>
                <select
                  value={categorySettings.paper_size || '80mm'}
                  onChange={(e) => handleSettingChange('paper_size', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="58mm">58mm</option>
                  <option value="80mm">80mm</option>
                  <option value="112mm">112mm</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Copy Count
                </label>
                <input
                  type="number"
                  min="1"
                  max="5"
                  value={categorySettings.copy_count || 1}
                  onChange={(e) => handleSettingChange('copy_count', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_logo ?? true}
                  onChange={(e) => handleSettingChange('print_logo', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Logo</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_order_number ?? true}
                  onChange={(e) => handleSettingChange('print_order_number', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Order Number</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_date_time ?? true}
                  onChange={(e) => handleSettingChange('print_date_time', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Date & Time</span>
              </label>
            </div>
          </div>
        )

      case 'payment':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.cash_enabled ?? true}
                    onChange={(e) => handleSettingChange('cash_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Cash Payments</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.card_enabled ?? true}
                    onChange={(e) => handleSettingChange('card_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Card Payments</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.contactless_enabled ?? true}
                    onChange={(e) => handleSettingChange('contactless_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Contactless Payments</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.mobile_payments ?? true}
                    onChange={(e) => handleSettingChange('mobile_payments', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Mobile Payments</span>
                </label>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Minimum Card Amount (€)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={categorySettings.minimum_card_amount || 0}
                    onChange={(e) => handleSettingChange('minimum_card_amount', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      checked={categorySettings.tip_enabled ?? true}
                      onChange={(e) => handleSettingChange('tip_enabled', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Enable Tips</span>
                  </label>
                  
                  {categorySettings.tip_enabled && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tip Percentages (comma-separated)
                      </label>
                      <input
                        type="text"
                        placeholder="15,18,20,25"
                        value={(categorySettings.tip_percentages || [15, 18, 20, 25]).join(',')}
                        onChange={(e) => {
                          const percentages = e.target.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p))
                          handleSettingChange('tip_percentages', percentages)
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )

      case 'tax':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Default Tax Rate (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={categorySettings.default_tax_rate || 8.25}
                  onChange={(e) => handleSettingChange('default_tax_rate', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tax Name
                </label>
                <input
                  type="text"
                  value={categorySettings.tax_name || 'VAT'}
                  onChange={(e) => handleSettingChange('tax_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Calculation Method
                </label>
                <select
                  value={categorySettings.tax_calculation_method || 'percentage'}
                  onChange={(e) => handleSettingChange('tax_calculation_method', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tax Rounding
                </label>
                <select
                  value={categorySettings.tax_rounding || 'nearest_cent'}
                  onChange={(e) => handleSettingChange('tax_rounding', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="nearest_cent">Nearest Cent</option>
                  <option value="round_up">Round Up</option>
                  <option value="round_down">Round Down</option>
                </select>
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.tax_inclusive ?? false}
                  onChange={(e) => handleSettingChange('tax_inclusive', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Tax Inclusive Pricing</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.auto_calculate_tax ?? true}
                  onChange={(e) => handleSettingChange('auto_calculate_tax', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Auto Calculate Tax</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.tax_exempt_enabled ?? true}
                  onChange={(e) => handleSettingChange('tax_exempt_enabled', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Allow Tax Exempt</span>
              </label>
            </div>
          </div>
        )

      case 'receipt':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Header Text
                </label>
                <textarea
                  rows={3}
                  value={categorySettings.header_text || 'Thank you for your order!'}
                  onChange={(e) => handleSettingChange('header_text', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Footer Text
                </label>
                <textarea
                  rows={3}
                  value={categorySettings.footer_text || 'Visit us again soon!'}
                  onChange={(e) => handleSettingChange('footer_text', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Receipt Width
                </label>
                <select
                  value={categorySettings.receipt_width || '80mm'}
                  onChange={(e) => handleSettingChange('receipt_width', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="58mm">58mm</option>
                  <option value="80mm">80mm</option>
                  <option value="112mm">112mm</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Font Size
                </label>
                <select
                  value={categorySettings.font_size || 'medium'}
                  onChange={(e) => handleSettingChange('font_size', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.show_logo ?? true}
                  onChange={(e) => handleSettingChange('show_logo', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Show Logo</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.show_qr_code ?? false}
                  onChange={(e) => handleSettingChange('show_qr_code', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Show QR Code</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_customer_copy ?? true}
                  onChange={(e) => handleSettingChange('print_customer_copy', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Customer Copy</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.email_receipts ?? false}
                  onChange={(e) => handleSettingChange('email_receipts', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Email Receipts</span>
              </label>
            </div>
          </div>
        )

      default:
        return <div>Select a category to configure settings</div>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Terminal Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Terminal Selection</h3>
        
        <div className="grid gap-4">
          {terminals.map((terminal) => (
            <div
              key={terminal.terminal_id}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedTerminal === terminal.terminal_id
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
              onClick={() => setSelectedTerminal(terminal.terminal_id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getTerminalStatusIcon(terminal.status)}
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{terminal.name}</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {terminal.terminal_id} • {terminal.ip_address} • {terminal.location}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Last seen: {new Date(terminal.last_heartbeat).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Version: {terminal.version}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Settings Categories */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Terminal Settings</h3>
          <div className="flex space-x-2 mt-4 sm:mt-0">
            <button
              onClick={saveSettings}
              disabled={saving || !selectedTerminal}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
              <span>{saving ? 'Saving...' : 'Save Settings'}</span>
            </button>
            
            <button
              onClick={syncAllSettings}
              disabled={syncing || !selectedTerminal}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {syncing ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
              <span>{syncing ? 'Syncing...' : 'Sync All'}</span>
            </button>
          </div>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap gap-2 mb-6">
          {categories.map((category) => {
            const Icon = category.icon
            return (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  activeCategory === category.id
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{category.name}</span>
              </button>
            )
          })}
        </div>

        {/* Settings Form */}
        <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
          {selectedTerminal ? (
            renderTerminalSettings()
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              Select a terminal to configure settings
            </div>
          )}
        </div>
      </div>
    </div>
  )
}