'use client'

import React from 'react'
import { cn } from '@/lib/utils'

// Import the glassmorphism CSS
import '@/styles/glassmorphism.css'

// GlassCard Component
interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  className?: string
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void
}

export const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
  ({ children, variant = 'primary', className = '', onClick, ...props }, ref) => {
    const baseClasses = 'glass-container glass-card'
    const variantClass = `glass-${variant}`
    const interactiveClass = onClick ? 'glass-interactive' : ''

    const combinedClasses = cn(baseClasses, variantClass, interactiveClass, className)

    return (
      <div
        ref={ref}
        className={combinedClasses}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
        onKeyDown={
          onClick
            ? e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  onClick(e as any)
                }
              }
            : undefined
        }
        {...props}
      >
        {children}
      </div>
    )
  }
)

GlassCard.displayName = 'GlassCard'

// GlassButton Component
interface GlassButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  className?: string
}

export const GlassButton = React.forwardRef<HTMLButtonElement, GlassButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'medium',
      disabled = false,
      loading = false,
      className = '',
      ...props
    },
    ref
  ) => {
    const baseClasses = 'glass-button'
    const variantClass = variant !== 'primary' ? `glass-${variant}` : ''
    const sizeClasses = {
      small: 'text-sm px-3 py-2 min-h-[36px]',
      medium: 'text-base px-4 py-3 min-h-[44px]',
      large: 'text-lg px-6 py-4 min-h-[52px]',
    }

    const combinedClasses = cn(
      baseClasses,
      variantClass,
      sizeClasses[size],
      'transition-all duration-200 ease-in-out',
      'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',
      disabled && 'opacity-50 cursor-not-allowed',
      loading && 'cursor-wait',
      className
    )

    return (
      <button
        ref={ref}
        className={combinedClasses}
        disabled={disabled || loading}
        aria-disabled={disabled || loading}
        aria-busy={loading}
        {...props}
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <svg
              className="animate-spin h-4 w-4"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
                fill="none"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            <span className="sr-only">Loading...</span>
            {children}
          </div>
        ) : (
          children
        )}
      </button>
    )
  }
)

GlassButton.displayName = 'GlassButton'

// GlassInput Component
interface GlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
}

export const GlassInput = React.forwardRef<HTMLInputElement, GlassInputProps>(
  ({ className = '', ...props }, ref) => {
    return <input ref={ref} className={cn('glass-input', className)} {...props} />
  }
)

GlassInput.displayName = 'GlassInput'

// GlassModal Component
interface GlassModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  className?: string
}

export const GlassModal = ({
  isOpen,
  onClose,
  title,
  children,
  className = '',
}: GlassModalProps) => {
  const modalRef = React.useRef<HTMLDivElement>(null)

  // Handle escape key and focus management
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'

      // Focus the modal when it opens
      setTimeout(() => {
        modalRef.current?.focus()
      }, 100)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <>
      <div
        className="glass-modal-backdrop"
        onClick={onClose}
        aria-hidden="true"
      />

      <div
        ref={modalRef}
        className={cn('glass-modal', className)}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'modal-title' : undefined}
        tabIndex={-1}
      >
        {title && (
          <h2 id="modal-title" className="text-xl font-semibold mb-4 glass-text-primary">
            {title}
          </h2>
        )}
        <div className="glass-text-secondary">
          {children}
        </div>
      </div>
    </>
  )
}

// GlassContainer - A generic container with glass effect
interface GlassContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  className?: string
}

export const GlassContainer = React.forwardRef<HTMLDivElement, GlassContainerProps>(
  ({ children, variant = 'primary', className = '', ...props }, ref) => {
    const baseClasses = 'glass-container'
    const variantClass = `glass-${variant}`

    const combinedClasses = cn(baseClasses, variantClass, className)

    return (
      <div ref={ref} className={combinedClasses} {...props}>
        {children}
      </div>
    )
  }
)

GlassContainer.displayName = 'GlassContainer'

// Card sub-components for better composition
interface GlassCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

export const GlassCardHeader = React.forwardRef<HTMLDivElement, GlassCardHeaderProps>(
  ({ children, className = '', ...props }, ref) => {
    return (
      <div ref={ref} className={cn('glass-card-header', className)} {...props}>
        {children}
      </div>
    )
  }
)

GlassCardHeader.displayName = 'GlassCardHeader'

interface GlassCardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode
  className?: string
}

export const GlassCardTitle = React.forwardRef<HTMLHeadingElement, GlassCardTitleProps>(
  ({ children, className = '', ...props }, ref) => {
    return (
      <h3 ref={ref} className={cn('glass-card-title text-lg font-semibold', className)} {...props}>
        {children}
      </h3>
    )
  }
)

GlassCardTitle.displayName = 'GlassCardTitle'

interface GlassCardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode
  className?: string
}

export const GlassCardDescription = React.forwardRef<
  HTMLParagraphElement,
  GlassCardDescriptionProps
>(({ children, className = '', ...props }, ref) => {
  return (
    <p ref={ref} className={cn('glass-card-description text-sm opacity-80', className)} {...props}>
      {children}
    </p>
  )
})

GlassCardDescription.displayName = 'GlassCardDescription'

interface GlassCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

export const GlassCardContent = React.forwardRef<HTMLDivElement, GlassCardContentProps>(
  ({ children, className = '', ...props }, ref) => {
    return (
      <div ref={ref} className={cn('glass-card-content', className)} {...props}>
        {children}
      </div>
    )
  }
)

GlassCardContent.displayName = 'GlassCardContent'
