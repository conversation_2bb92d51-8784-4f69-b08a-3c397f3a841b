import React, { useState, useEffect } from "react";
import { HashRouter, Routes, Route } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { ThemeProvider } from "./contexts/theme-context";
import RefactoredMainLayout from "./components/RefactoredMainLayout";
import NewOrderPage from "./pages/NewOrderPage";
import ErrorBoundary from "./components/ErrorBoundary";

// Simple login component
function LoginPage({ onLogin }: { onLogin: (pin: string) => Promise<boolean> }) {
  const [pin, setPin] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleNumberClick = (number: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + number);
      setError("");
    }
  };

  const handleClear = () => {
    setPin("");
    setError("");
  };

  const handleBackspace = () => {
    setPin(prev => prev.slice(0, -1));
    setError("");
  };

  const handleLoginClick = async () => {
    if (!pin) {
      setError("Please enter your PIN");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const success = await onLogin(pin);
      if (!success) {
        setError("Invalid PIN. Try 1234, 0000, or admin");
      }
    } catch (err) {
      setError("Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const numbers = [
    ['1', '2', '3'],
    ['4', '5', '6'], 
    ['7', '8', '9'],
    ['Clear', '0', '⌫']
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center p-4">
      <div className="bg-white/20 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/30 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Creperie POS
          </h1>
          <p className="text-white/80">
            Enter your PIN to continue
          </p>
        </div>

        <div className="mb-6">
          <div className="bg-white/10 border border-white/30 rounded-xl p-4 text-center">
            <div className="text-2xl text-white font-mono tracking-widest">
              {pin.replace(/./g, '●') || '──────'}
            </div>
          </div>
          {error && (
            <p className="text-red-300 text-sm mt-2 text-center">{error}</p>
          )}
        </div>

        <div className="grid grid-cols-3 gap-3 mb-6">
          {numbers.flat().map((item, index) => (
            <button
              key={index}
              onClick={() => {
                if (item === 'Clear') {
                  handleClear();
                } else if (item === '⌫') {
                  handleBackspace();
                } else {
                  handleNumberClick(item);
                }
              }}
              disabled={isLoading}
              className={`
                h-14 rounded-xl backdrop-blur-sm border border-white/30 transition-all duration-200 font-semibold text-lg
                ${item === 'Clear' 
                  ? 'bg-red-500/20 hover:bg-red-500/30 text-red-200 col-span-1' 
                  : item === '⌫'
                  ? 'bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-200'
                  : 'bg-white/20 hover:bg-white/30 text-white'
                }
                disabled:opacity-50 disabled:cursor-not-allowed
                active:scale-95 transform
              `}
            >
              {item}
            </button>
          ))}
        </div>

        <button
          onClick={handleLoginClick}
          disabled={!pin || isLoading}
          className="w-full bg-green-500/20 hover:bg-green-500/30 disabled:bg-white/10 text-white px-6 py-4 rounded-xl backdrop-blur-sm border border-green-400/30 transition-all duration-300 disabled:cursor-not-allowed font-semibold text-lg"
        >
          {isLoading ? "Logging in..." : "Login"}
        </button>

        <div className="mt-6 text-center">
          <p className="text-white/60 text-sm">
            POS System v1.0 - Ready for service
          </p>
          <p className="text-white/40 text-xs mt-1">
            Try: 1234, 0000, or admin
          </p>
        </div>
      </div>
    </div>
  );
}

// Simple MainLayout placeholder that shows we're working on it
function MainLayoutPlaceholder() {
  const handleLogout = () => {
    localStorage.removeItem("pos-user");
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-400 to-blue-600 flex items-center justify-center p-4">
      <div className="bg-white/20 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/30 w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">🎉 POS System Ready!</h1>
          <p className="text-white/80 mb-6">
            Login successful! The full MainLayout with all POS functionality is being restored.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-8">
          <div className="bg-white/10 rounded-xl p-4 text-center">
            <h3 className="text-white font-semibold mb-2">📊 Dashboard</h3>
            <p className="text-white/70 text-sm">Order management & analytics</p>
          </div>
          <div className="bg-white/10 rounded-xl p-4 text-center">
            <h3 className="text-white font-semibold mb-2">📦 Inventory</h3>
            <p className="text-white/70 text-sm">Stock management</p>
          </div>
          <div className="bg-white/10 rounded-xl p-4 text-center">
            <h3 className="text-white font-semibold mb-2">📈 Reports</h3>
            <p className="text-white/70 text-sm">Sales analytics</p>
          </div>
          <div className="bg-white/10 rounded-xl p-4 text-center">
            <h3 className="text-white font-semibold mb-2">👥 Customers</h3>
            <p className="text-white/70 text-sm">Customer management</p>
          </div>
        </div>

        <div className="text-center">
          <p className="text-white/60 text-sm mb-4">
            All your POS features are being restored step by step with proper error handling.
          </p>
          <button
            onClick={handleLogout}
            className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl backdrop-blur-sm border border-white/30 transition-all duration-300"
          >
            Logout
          </button>
        </div>
      </div>
    </div>
  );
}

function App() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if user is logged in on app start
  useEffect(() => {
    const storedUser = localStorage.getItem("pos-user");
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (err) {
        localStorage.removeItem("pos-user");
      }
    }
    setIsLoading(false);
  }, []);

  // Login function that updates state directly
  const handleLogin = async (pin: string) => {
    if (pin === "1234" || pin === "0000" || pin === "admin") {
      const userData = {
        id: "user-1",
        staffId: pin,
        staffName: `Staff ${pin}`,
        role: { name: "admin", permissions: ["*"] },
        sessionId: `session-${Date.now()}`,
        loginTime: new Date().toISOString()
      };
      
      localStorage.setItem("pos-user", JSON.stringify(userData));
      setUser(userData);
      return true;
    }
    return false;
  };

  // Logout function
  const handleLogout = () => {
    localStorage.removeItem("pos-user");
    setUser(null);
  };

  // Show loading spinner during initial check
  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-t-transparent border-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-800">Loading POS System...</p>
          <p className="text-sm text-gray-500 mt-2">Debug: App is loading...</p>
        </div>
      </div>
    );
  }

  // Show login page if no user
  if (!user) {
    return <LoginPage onLogin={handleLogin} />;
  }

  // Show main POS interface if logged in
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <HashRouter>
          <div className="min-h-screen">
            <Routes>
              <Route path="/" element={<RefactoredMainLayout onLogout={handleLogout} />} />
              <Route path="/dashboard" element={<RefactoredMainLayout onLogout={handleLogout} />} />
              <Route path="/new-order" element={<NewOrderPage />} />
              <Route path="*" element={<RefactoredMainLayout onLogout={handleLogout} />} />
            </Routes>
            <Toaster
              position="top-center"
              toastOptions={{
                duration: 3000,
                style: {
                  background: "rgba(0, 0, 0, 0.8)",
                  color: "#fff",
                  borderRadius: "12px",
                  border: "1px solid rgba(255, 255, 255, 0.2)",
                },
              }}
            />
          </div>
        </HashRouter>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
